<template>
  <div class="bg-light-bg-primary dark:bg-dark-bg-primary min-h-screen">
    <!-- Hero Section -->
    <HeroSection />

    <!-- Featured Categories -->
    <CategoriesSection />

    <!-- Featured Products -->
    <FeaturedProductsSection />

    <!-- Stats Section -->
    <StatsSection />

    <!-- Newsletter Section -->
    <NewsletterSection />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import HeroSection from '@/components/sections/HeroSection.vue'
import CategoriesSection from '@/components/sections/CategoriesSection.vue'
import FeaturedProductsSection from '@/components/sections/FeaturedProductsSection.vue'
import StatsSection from '@/components/sections/StatsSection.vue'
import NewsletterSection from '@/components/sections/NewsletterSection.vue'

onMounted(() => {
  console.log('🏠 Home page loaded successfully')
})
</script>


