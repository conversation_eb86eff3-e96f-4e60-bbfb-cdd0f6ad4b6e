<template>
  <section class="section-padding bg-light-bg-secondary dark:bg-dark-bg-secondary">
    <div class="container-custom">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl lg:text-4xl font-bold text-light-text-primary dark:text-dark-text-primary mb-4">
          Why Choose Sport Shop?
        </h2>
        <p class="text-lg text-light-text-secondary dark:text-dark-text-secondary max-w-2xl mx-auto">
          Join thousands of satisfied customers who trust us for their sports and fitness needs.
        </p>
      </div>

      <!-- Stats Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <StatCard
          v-for="stat in stats"
          :key="stat.id"
          :stat="stat"
          class="animate-fade-in"
          :style="{ animationDelay: `${stats.indexOf(stat) * 0.1}s` }"
        />
      </div>

      <!-- Features Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div 
          v-for="feature in features"
          :key="feature.id"
          class="text-center p-6 rounded-xl bg-light-bg-primary dark:bg-dark-bg-primary border border-light-border-secondary dark:border-dark-border-secondary hover:shadow-lg transition-all duration-300 animate-fade-in"
          :style="{ animationDelay: `${features.indexOf(feature) * 0.15}s` }"
        >
          <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-sport dark:bg-gradient-sport-dark flex items-center justify-center">
            <n-icon size="32" class="text-white">
              <component :is="feature.icon" />
            </n-icon>
          </div>
          <h3 class="text-xl font-bold text-light-text-primary dark:text-dark-text-primary mb-2">
            {{ feature.title }}
          </h3>
          <p class="text-light-text-secondary dark:text-dark-text-secondary">
            {{ feature.description }}
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NIcon } from 'naive-ui'
import { 
  ShieldCheckmarkOutline,
  FlashOutline,
  HeartOutline,
  TrophyOutline,
  PeopleOutline,
  StarOutline,
  CheckmarkCircleOutline
} from '@vicons/ionicons5'
import { StatCard } from '@/components/examples'
import type { StatData } from '@/components/examples'

// Stats data
const stats = ref<StatData[]>([
  {
    id: 1,
    title: 'Happy Customers',
    value: '10,000+',
    change: '+15%',
    trend: 'up',
    icon: PeopleOutline,
    color: 'sport',
    progress: 85
  },
  {
    id: 2,
    title: 'Products Sold',
    value: '50,000+',
    change: '+25%',
    trend: 'up',
    icon: TrophyOutline,
    color: 'success',
    progress: 92
  },
  {
    id: 3,
    title: 'Customer Rating',
    value: '4.9/5',
    change: '+0.2',
    trend: 'up',
    icon: StarOutline,
    color: 'warning',
    progress: 98
  },
  {
    id: 4,
    title: 'Years Experience',
    value: '15+',
    change: 'Growing',
    trend: 'up',
    icon: CheckmarkCircleOutline,
    color: 'info',
    progress: 100
  }
])

// Features data
const features = ref([
  {
    id: 1,
    title: 'Premium Quality',
    description: 'Only the finest materials and craftsmanship in every product we offer.',
    icon: ShieldCheckmarkOutline
  },
  {
    id: 2,
    title: 'Fast Delivery',
    description: 'Quick and reliable shipping to get your gear when you need it most.',
    icon: FlashOutline
  },
  {
    id: 3,
    title: 'Customer Care',
    description: 'Dedicated support team ready to help with any questions or concerns.',
    icon: HeartOutline
  }
])
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
