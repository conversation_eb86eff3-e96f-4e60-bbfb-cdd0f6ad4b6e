<template>
  <section class="section-padding bg-light-bg-primary dark:bg-dark-bg-primary">
    <div class="container-custom">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl lg:text-4xl font-bold text-light-text-primary dark:text-dark-text-primary mb-4">
          Featured Products
        </h2>
        <p class="text-lg text-light-text-secondary dark:text-dark-text-secondary max-w-2xl mx-auto">
          Discover our handpicked selection of premium sports equipment and apparel from top brands.
        </p>
      </div>

      <!-- Filter Tabs -->
      <div class="flex justify-center mb-8">
        <div class="flex flex-wrap gap-2 p-1 bg-light-bg-secondary dark:bg-dark-bg-secondary rounded-lg">
          <button
            v-for="filter in filters"
            :key="filter.value"
            :class="[
              'px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
              activeFilter === filter.value
                ? 'bg-light-accent-sport dark:bg-dark-accent-sport text-white'
                : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-light-text-primary dark:hover:text-dark-text-primary'
            ]"
            @click="setActiveFilter(filter.value)"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <ProductCard
          v-for="product in filteredProducts"
          :key="product.id"
          :product="product"
          @click="handleProductClick"
          @add-to-cart="handleAddToCart"
          @toggle-favorite="handleToggleFavorite"
          class="animate-fade-in"
          :style="{ animationDelay: `${filteredProducts.indexOf(product) * 0.05}s` }"
        />
      </div>

      <!-- View All Button -->
      <div class="text-center mt-12">
        <n-button 
          type="primary"
          size="large"
          class="btn-primary px-8 py-3"
          @click="handleViewAllProducts"
        >
          <template #icon>
            <n-icon size="18">
              <ShoppingBagOutline />
            </n-icon>
          </template>
          View All Products
        </n-button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { ShoppingBagOutline } from '@vicons/ionicons5'
import { ProductCard, generateSampleProducts } from '@/components/examples'
import type { Product } from '@/components/examples'

// Filter options
const filters = [
  { label: 'All', value: 'all' },
  { label: 'New Arrivals', value: 'new' },
  { label: 'Best Sellers', value: 'bestseller' },
  { label: 'On Sale', value: 'sale' }
]

// State
const activeFilter = ref('all')
const products = ref<Product[]>(generateSampleProducts(12))

// Computed
const filteredProducts = computed(() => {
  switch (activeFilter.value) {
    case 'new':
      return products.value.slice(0, 8) // Show first 8 as "new"
    case 'bestseller':
      return products.value.filter(p => p.rating >= 4).slice(0, 8)
    case 'sale':
      return products.value.filter(p => p.originalPrice && p.originalPrice > p.price).slice(0, 8)
    default:
      return products.value.slice(0, 8) // Show first 8 products
  }
})

// Methods
const setActiveFilter = (filter: string) => {
  activeFilter.value = filter
}

const handleProductClick = (product: Product) => {
  console.log('Navigate to product:', product.name)
  // Add navigation logic here
}

const handleAddToCart = (product: Product) => {
  console.log('Add to cart:', product.name)
  // Add cart logic here
}

const handleToggleFavorite = (product: Product) => {
  console.log('Toggle favorite:', product.name)
  // Add favorite logic here
  const index = products.value.findIndex(p => p.id === product.id)
  if (index !== -1) {
    products.value[index].isFavorite = !products.value[index].isFavorite
  }
}

const handleViewAllProducts = () => {
  console.log('Navigate to all products')
  // Add navigation logic here
}
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
