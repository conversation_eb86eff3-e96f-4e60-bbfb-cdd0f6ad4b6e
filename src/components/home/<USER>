<template>
  <section class="section-padding bg-gradient-sport dark:bg-gradient-sport-dark relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23ffffff\" fill-opacity=\"0.1\"><circle cx=\"30\" cy=\"30\" r=\"2\"/></g></g></svg>');"></div>
    </div>

    <div class="container-custom relative">
      <div class="max-w-4xl mx-auto text-center text-white">
        <!-- Header -->
        <div class="mb-8">
          <h2 class="text-3xl lg:text-4xl font-bold mb-4">
            Join Our Sports Community
          </h2>
          <p class="text-xl opacity-90 leading-relaxed">
            Subscribe to our newsletter for exclusive deals, fitness tips, and early access to new products.
          </p>
        </div>

        <!-- Newsletter Form -->
        <div class="max-w-md mx-auto mb-8">
          <div class="flex flex-col sm:flex-row gap-3">
            <div class="flex-1">
              <n-input
                v-model:value="email"
                type="email"
                placeholder="Enter your email address"
                size="large"
                class="newsletter-input"
                :class="{ 'error': emailError }"
                @keyup.enter="handleSubscribe"
              >
                <template #prefix>
                  <n-icon size="18" class="text-light-text-muted dark:text-dark-text-muted">
                    <MailOutline />
                  </n-icon>
                </template>
              </n-input>
              <div v-if="emailError" class="text-red-300 text-sm mt-1 text-left">
                {{ emailError }}
              </div>
            </div>
            <n-button
              type="primary"
              size="large"
              class="bg-white text-light-accent-sport hover:bg-gray-100 font-semibold px-8"
              :loading="isSubscribing"
              @click="handleSubscribe"
            >
              <template #icon>
                <n-icon size="18">
                  <SendOutline />
                </n-icon>
              </template>
              Subscribe
            </n-button>
          </div>
        </div>

        <!-- Benefits -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div 
            v-for="benefit in benefits"
            :key="benefit.id"
            class="flex items-center justify-center gap-3 text-white/90"
          >
            <n-icon size="20" class="text-white">
              <component :is="benefit.icon" />
            </n-icon>
            <span class="text-sm font-medium">{{ benefit.text }}</span>
          </div>
        </div>

        <!-- Privacy Notice -->
        <p class="text-sm text-white/70 leading-relaxed">
          By subscribing, you agree to our 
          <a href="#" class="underline hover:text-white transition-colors">Terms of Service</a> 
          and 
          <a href="#" class="underline hover:text-white transition-colors">Privacy Policy</a>.
          Unsubscribe at any time.
        </p>

        <!-- Success Message -->
        <div 
          v-if="showSuccess"
          class="mt-6 p-4 bg-white/20 backdrop-blur-sm rounded-lg border border-white/30"
        >
          <div class="flex items-center justify-center gap-2 text-white">
            <n-icon size="20">
              <CheckmarkCircleOutline />
            </n-icon>
            <span class="font-medium">Thank you for subscribing! Check your email for confirmation.</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NInput, NButton, NIcon } from 'naive-ui'
import { 
  MailOutline, 
  SendOutline, 
  CheckmarkCircleOutline,
  GiftOutline,
  FlashOutline,
  StarOutline
} from '@vicons/ionicons5'

// State
const email = ref('')
const emailError = ref('')
const isSubscribing = ref(false)
const showSuccess = ref(false)

// Benefits data
const benefits = ref([
  {
    id: 1,
    text: 'Exclusive Deals',
    icon: GiftOutline
  },
  {
    id: 2,
    text: 'Early Access',
    icon: FlashOutline
  },
  {
    id: 3,
    text: 'Fitness Tips',
    icon: StarOutline
  }
])

// Methods
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const handleSubscribe = async () => {
  // Reset states
  emailError.value = ''
  
  // Validate email
  if (!email.value) {
    emailError.value = 'Email is required'
    return
  }
  
  if (!validateEmail(email.value)) {
    emailError.value = 'Please enter a valid email address'
    return
  }

  // Simulate subscription process
  isSubscribing.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Show success message
    showSuccess.value = true
    email.value = ''
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      showSuccess.value = false
    }, 5000)
    
    console.log('Newsletter subscription successful')
  } catch (error) {
    emailError.value = 'Subscription failed. Please try again.'
    console.error('Newsletter subscription error:', error)
  } finally {
    isSubscribing.value = false
  }
}
</script>

<style scoped>
.newsletter-input :deep(.n-input__input-el) {
  background: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
}

.newsletter-input :deep(.n-input__input-el::placeholder) {
  color: #666 !important;
}

.newsletter-input.error :deep(.n-input__border) {
  border-color: #ef4444 !important;
}

.newsletter-input :deep(.n-input__border) {
  border-color: transparent !important;
}

.newsletter-input :deep(.n-input__state-border) {
  border-color: rgba(255, 255, 255, 0.3) !important;
}
</style>
